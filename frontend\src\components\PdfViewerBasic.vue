<template>
  <div class="pdf-viewer-basic">
    <div class="pdf-header">
      <h3>PDF 查看器测试</h3>
      <div v-if="pageCount">页数: {{ pageNum }} / {{ pageCount }}</div>
      <div v-if="loading">正在加载...</div>
      <div v-if="error" style="color: red;">错误: {{ error }}</div>
    </div>

    <div class="pdf-controls">
      <button @click="prevPage" :disabled="pageNum <= 1">上一页</button>
      <button @click="nextPage" :disabled="pageNum >= pageCount">下一页</button>
      <button @click="zoomOut">缩小</button>
      <span>{{ Math.round(scale * 100) }}%</span>
      <button @click="zoomIn">放大</button>
      <button @click="retry">重新加载</button>
    </div>

    <div class="pdf-content">
      <canvas 
        ref="canvas" 
        v-show="!loading && !error"
        style="border: 1px solid #ccc; max-width: 100%;"
      ></canvas>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PdfViewerBasic',
  props: {
    pdfUrl: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      loading: false,
      error: null,
      pageNum: 0,
      pageCount: 0,
      scale: 1.0
    };
  },
  mounted() {
    console.log('组件已挂载，开始初始化...');
    this.initAndLoad();
  },
  watch: {
    pdfUrl() {
      this.initAndLoad();
    }
  },
  methods: {
    async initAndLoad() {
      try {
        await this.initPdfJs();
        await this.loadPdf();
      } catch (err) {
        console.error('初始化失败:', err);
        this.error = err.message;
      }
    },

    async initPdfJs() {
      try {
        console.log('正在初始化 PDF.js...');
        
        // 动态导入 PDF.js
        const pdfjsLib = await import('pdfjs-dist');
        
        // 设置 worker
        pdfjsLib.GlobalWorkerOptions.workerSrc = new URL(
          'pdfjs-dist/build/pdf.worker.min.mjs',
          import.meta.url
        ).href;
        
        // 存储到实例属性（非响应式）
        this.pdfjsLib = pdfjsLib;
        
        console.log('PDF.js 初始化成功，版本:', pdfjsLib.version);
        console.log('Worker URL:', pdfjsLib.GlobalWorkerOptions.workerSrc);
        
      } catch (err) {
        throw new Error(`PDF.js 初始化失败: ${err.message}`);
      }
    },

    async loadPdf() {
      if (!this.pdfjsLib) {
        throw new Error('PDF.js 未初始化');
      }

      this.loading = true;
      this.error = null;
      this.pageNum = 0;
      this.pageCount = 0;

      try {
        console.log('开始加载 PDF:', this.pdfUrl);

        // 清理之前的文档
        if (this.pdfDocument) {
          this.pdfDocument.destroy();
          this.pdfDocument = null;
        }

        // 加载新文档
        const loadingTask = this.pdfjsLib.getDocument({
          url: this.pdfUrl
        });

        this.pdfDocument = await loadingTask.promise;
        this.pageCount = this.pdfDocument.numPages;

        console.log('PDF 加载成功，总页数:', this.pageCount);

        // 等待 DOM 更新
        await this.$nextTick();
        
        // 渲染第一页
        await this.renderPage(1);
        
        this.loading = false;

      } catch (err) {
        console.error('PDF 加载失败:', err);
        this.error = `加载失败: ${err.message}`;
        this.loading = false;
      }
    },

    async renderPage(pageNumber) {
      if (!this.pdfDocument || pageNumber < 1 || pageNumber > this.pageCount) {
        return;
      }

      try {
        console.log('开始渲染页面:', pageNumber);

        // 获取页面
        const page = await this.pdfDocument.getPage(pageNumber);
        
        // 等待 DOM 更新
        await this.$nextTick();
        
        // 获取 canvas
        const canvas = this.$refs.canvas;
        if (!canvas) {
          throw new Error('Canvas 元素未找到');
        }

        const context = canvas.getContext('2d');
        const viewport = page.getViewport({ scale: this.scale });

        console.log('视口尺寸:', viewport.width, 'x', viewport.height);

        // 设置 canvas 尺寸
        canvas.width = viewport.width;
        canvas.height = viewport.height;

        // 清除画布
        context.clearRect(0, 0, canvas.width, canvas.height);

        // 渲染页面
        const renderContext = {
          canvasContext: context,
          viewport: viewport
        };

        console.log('开始渲染...');
        const renderTask = page.render(renderContext);
        await renderTask.promise;

        this.pageNum = pageNumber;
        console.log('页面渲染完成:', pageNumber);

      } catch (err) {
        console.error('页面渲染失败:', err);
        this.error = `渲染页面 ${pageNumber} 失败: ${err.message}`;
      }
    },

    async prevPage() {
      if (this.pageNum > 1) {
        await this.renderPage(this.pageNum - 1);
      }
    },

    async nextPage() {
      if (this.pageNum < this.pageCount) {
        await this.renderPage(this.pageNum + 1);
      }
    },

    async zoomIn() {
      this.scale = Math.min(this.scale * 1.2, 3.0);
      if (this.pageNum > 0) {
        await this.renderPage(this.pageNum);
      }
    },

    async zoomOut() {
      this.scale = Math.max(this.scale / 1.2, 0.3);
      if (this.pageNum > 0) {
        await this.renderPage(this.pageNum);
      }
    },

    async retry() {
      await this.loadPdf();
    }
  },

  beforeUnmount() {
    if (this.pdfDocument) {
      this.pdfDocument.destroy();
    }
  }
}
</script>

<style scoped>
.pdf-viewer-basic {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.pdf-header {
  margin-bottom: 20px;
  padding: 10px;
  background: #f5f5f5;
  border-radius: 4px;
}

.pdf-controls {
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
  align-items: center;
}

.pdf-controls button {
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  cursor: pointer;
}

.pdf-controls button:hover:not(:disabled) {
  background: #f0f0f0;
}

.pdf-controls button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pdf-content {
  text-align: center;
}
</style>
