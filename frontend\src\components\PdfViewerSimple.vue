<template>
  <div class="pdf-viewer-simple">
    <div class="pdf-toolbar">
      <div class="pdf-info">
        <span v-if="pageNum && pageCount">{{ pageNum }} / {{ pageCount }}</span>
        <span v-else-if="loading">加载中...</span>
        <span v-else>未加载</span>
      </div>
      <div class="pdf-controls">
        <button @click="prevPage" :disabled="pageNum <= 1 || loading">上一页</button>
        <button @click="nextPage" :disabled="pageNum >= pageCount || loading">下一页</button>
        <button @click="zoomOut" :disabled="loading">缩小</button>
        <span>{{ Math.round(scale * 100) }}%</span>
        <button @click="zoomIn" :disabled="loading">放大</button>
        <button @click="downloadPdf" :disabled="loading">下载</button>
      </div>
    </div>
    
    <div class="pdf-container">
      <div v-if="loading" class="pdf-loading">
        <div class="spinner"></div>
        <p>正在加载 PDF...</p>
      </div>
      
      <div v-else-if="error" class="pdf-error">
        <h4>加载失败</h4>
        <p>{{ error }}</p>
        <button @click="retry">重试</button>
      </div>
      
      <div v-else class="pdf-content">
        <canvas ref="pdfCanvas" class="pdf-canvas"></canvas>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PdfViewerSimple',
  props: {
    pdfUrl: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      loading: false,
      error: null,
      pageNum: 0,
      pageCount: 0,
      scale: 1.0
    };
  },
  // 非响应式属性，避免 Vue 破坏 PDF.js 对象
  beforeCreate() {
    this._pdfDocument = null;
    this._currentPage = null;
    this._renderTask = null;
    this._pdfjsLib = null;
  },
  async mounted() {
    await this.initPdfJs();
    if (this.pdfUrl && !this.error) {
      await this.loadDocument();
    }
  },
  beforeUnmount() {
    this.cleanup();
  },
  watch: {
    pdfUrl: {
      handler(newUrl) {
        if (newUrl) {
          this.loadDocument();
        }
      }
    }
  },
  methods: {
    // 初始化 PDF.js
    async initPdfJs() {
      try {
        console.log('初始化 PDF.js...');
        
        // 动态导入
        this._pdfjsLib = await import('pdfjs-dist');
        
        // 设置 worker
        this._pdfjsLib.GlobalWorkerOptions.workerSrc = new URL(
          'pdfjs-dist/build/pdf.worker.min.mjs',
          import.meta.url
        ).href;
        
        console.log('PDF.js 初始化成功，版本:', this._pdfjsLib.version);
        
      } catch (err) {
        console.error('PDF.js 初始化失败:', err);
        this.error = `初始化失败: ${err.message}`;
      }
    },

    // 加载文档
    async loadDocument() {
      if (!this._pdfjsLib) {
        this.error = 'PDF.js 未初始化';
        return;
      }

      this.cleanup();
      this.loading = true;
      this.error = null;
      this.pageNum = 0;
      this.pageCount = 0;

      try {
        console.log('加载文档:', this.pdfUrl);

        const loadingTask = this._pdfjsLib.getDocument({
          url: this.pdfUrl
        });

        this._pdfDocument = await loadingTask.promise;
        this.pageCount = this._pdfDocument.numPages;
        
        console.log('文档加载成功，页数:', this.pageCount);

        // 加载第一页
        await this.loadPage(1);
        
        this.loading = false;

      } catch (err) {
        console.error('文档加载失败:', err);
        this.error = `加载失败: ${err.message}`;
        this.loading = false;
      }
    },

    // 加载页面
    async loadPage(pageNumber) {
      if (!this._pdfDocument || pageNumber < 1 || pageNumber > this.pageCount) {
        return;
      }

      try {
        console.log('加载页面:', pageNumber);

        // 取消当前渲染
        if (this._renderTask) {
          this._renderTask.cancel();
          this._renderTask = null;
        }

        // 获取页面
        this._currentPage = await this._pdfDocument.getPage(pageNumber);
        
        // 渲染页面
        await this.renderPage();
        
        this.pageNum = pageNumber;

      } catch (err) {
        console.error('页面加载失败:', err);
        this.error = `页面 ${pageNumber} 加载失败: ${err.message}`;
      }
    },

    // 渲染页面
    async renderPage() {
      if (!this._currentPage) return;

      try {
        const canvas = this.$refs.pdfCanvas;
        if (!canvas) {
          throw new Error('Canvas 未找到');
        }

        const context = canvas.getContext('2d');
        const viewport = this._currentPage.getViewport({ scale: this.scale });

        // 设置 canvas 尺寸
        canvas.width = viewport.width;
        canvas.height = viewport.height;

        // 清除画布
        context.clearRect(0, 0, canvas.width, canvas.height);

        // 渲染
        const renderContext = {
          canvasContext: context,
          viewport: viewport
        };

        this._renderTask = this._currentPage.render(renderContext);
        await this._renderTask.promise;
        this._renderTask = null;

        console.log('页面渲染成功');

      } catch (err) {
        if (err.name !== 'RenderingCancelledException') {
          console.error('渲染失败:', err);
          this.error = `渲染失败: ${err.message}`;
        }
      }
    },

    // 导航方法
    async prevPage() {
      if (this.pageNum > 1) {
        await this.loadPage(this.pageNum - 1);
      }
    },

    async nextPage() {
      if (this.pageNum < this.pageCount) {
        await this.loadPage(this.pageNum + 1);
      }
    },

    async zoomIn() {
      this.scale = Math.min(this.scale * 1.2, 3.0);
      await this.renderPage();
    },

    async zoomOut() {
      this.scale = Math.max(this.scale / 1.2, 0.3);
      await this.renderPage();
    },

    async retry() {
      await this.loadDocument();
    },

    downloadPdf() {
      if (this.pdfUrl) {
        window.open(this.pdfUrl, '_blank');
      }
    },

    // 清理资源
    cleanup() {
      try {
        if (this._renderTask) {
          this._renderTask.cancel();
          this._renderTask = null;
        }
      } catch (e) {
        console.warn('清理渲染任务失败:', e);
      }

      try {
        if (this._currentPage) {
          this._currentPage = null;
        }
      } catch (e) {
        console.warn('清理页面失败:', e);
      }

      try {
        if (this._pdfDocument) {
          this._pdfDocument.destroy();
          this._pdfDocument = null;
        }
      } catch (e) {
        console.warn('清理文档失败:', e);
      }
    }
  }
}
</script>

<style scoped>
.pdf-viewer-simple {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;
}

.pdf-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #ddd;
}

.pdf-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pdf-controls button {
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  cursor: pointer;
}

.pdf-controls button:hover:not(:disabled) {
  background: #f0f0f0;
}

.pdf-controls button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pdf-container {
  flex: 1;
  overflow: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.pdf-content {
  display: flex;
  justify-content: center;
}

.pdf-canvas {
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  max-width: 100%;
  height: auto;
}

.pdf-loading {
  text-align: center;
  color: #666;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.pdf-error {
  text-align: center;
  color: #dc2626;
}

.pdf-error button {
  margin-top: 16px;
  padding: 8px 16px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}
</style>
