<template>
  <div class="pdf-test-page">
    <div class="test-header">
      <h1>PDF.js 5.x 测试页面</h1>
      <p>测试 PDF 查看器在不同环境下的兼容性</p>
    </div>

    <div class="test-controls">
      <div class="url-input">
        <label>PDF URL:</label>
        <input 
          v-model="testPdfUrl" 
          type="text" 
          placeholder="输入 PDF 文件 URL"
          class="url-field"
        />
        <button @click="loadTestPdf" class="load-btn">加载 PDF</button>
      </div>

      <div class="preset-pdfs">
        <h3>预设测试 PDF:</h3>
        <div class="preset-buttons">
          <button 
            v-for="preset in presetPdfs" 
            :key="preset.name"
            @click="loadPresetPdf(preset.url)"
            class="preset-btn"
          >
            {{ preset.name }}
          </button>
        </div>
      </div>

      <div class="test-info">
        <div class="info-item">
          <strong>PDF.js 版本:</strong> {{ pdfjsVersion }}
        </div>
        <div class="info-item">
          <strong>浏览器:</strong> {{ browserInfo }}
        </div>
        <div class="info-item">
          <strong>当前 URL:</strong> {{ currentPdfUrl || '未加载' }}
        </div>
      </div>
    </div>

    <div class="pdf-viewer-container">
      <div v-if="!currentPdfUrl" class="no-pdf">
        <i class="fas fa-file-pdf"></i>
        <p>请选择一个 PDF 文件进行测试</p>
      </div>
      
      <PdfViewerModern
        v-else
        :pdf-url="currentPdfUrl"
        :title="currentTitle"
        :key="pdfKey"
      />
    </div>

    <div class="debug-panel">
      <h3>调试信息</h3>
      <div class="debug-content">
        <div v-for="log in debugLogs" :key="log.id" :class="['debug-log', log.type]">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
      <button @click="clearLogs" class="clear-logs-btn">清除日志</button>
    </div>
  </div>
</template>

<script>
import PdfViewerModern from '@/components/PdfViewerModern.vue'

export default {
  name: 'PdfTest',
  components: {
    PdfViewerModern
  },
  data() {
    return {
      testPdfUrl: '',
      currentPdfUrl: '',
      currentTitle: 'PDF 测试',
      pdfKey: 0,
      pdfjsVersion: '',
      browserInfo: '',
      debugLogs: [],
      logId: 0,
      presetPdfs: [
        {
          name: '示例 PDF 1',
          url: 'https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf'
        },
        {
          name: '示例 PDF 2', 
          url: 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf'
        },
        {
          name: '本地测试 PDF',
          url: '/test.pdf'
        }
      ]
    }
  },
  async mounted() {
    this.initDebugInfo();
    this.setupConsoleCapture();
    this.addLog('info', 'PDF 测试页面已加载');
  },
  methods: {
    async initDebugInfo() {
      try {
        // 获取 PDF.js 版本信息
        const pdfjsLib = await import('pdfjs-dist');
        this.pdfjsVersion = pdfjsLib.version || '未知';
        
        // 获取浏览器信息
        this.browserInfo = this.getBrowserInfo();
        
        this.addLog('info', `PDF.js 版本: ${this.pdfjsVersion}`);
        this.addLog('info', `浏览器: ${this.browserInfo}`);
      } catch (err) {
        this.addLog('error', `获取版本信息失败: ${err.message}`);
      }
    },

    getBrowserInfo() {
      const ua = navigator.userAgent;
      if (ua.includes('Chrome')) return 'Chrome';
      if (ua.includes('Firefox')) return 'Firefox';
      if (ua.includes('Safari')) return 'Safari';
      if (ua.includes('Edge')) return 'Edge';
      if (ua.includes('DingTalk')) return 'DingTalk';
      return 'Unknown';
    },

    setupConsoleCapture() {
      // 捕获控制台日志
      const originalLog = console.log;
      const originalError = console.error;
      const originalWarn = console.warn;

      console.log = (...args) => {
        this.addLog('info', args.join(' '));
        originalLog.apply(console, args);
      };

      console.error = (...args) => {
        this.addLog('error', args.join(' '));
        originalError.apply(console, args);
      };

      console.warn = (...args) => {
        this.addLog('warn', args.join(' '));
        originalWarn.apply(console, args);
      };
    },

    addLog(type, message) {
      const now = new Date();
      const time = now.toLocaleTimeString();
      
      this.debugLogs.unshift({
        id: this.logId++,
        type,
        time,
        message
      });

      // 限制日志数量
      if (this.debugLogs.length > 50) {
        this.debugLogs = this.debugLogs.slice(0, 50);
      }
    },

    loadTestPdf() {
      if (!this.testPdfUrl.trim()) {
        this.addLog('warn', '请输入 PDF URL');
        return;
      }
      
      this.currentPdfUrl = this.testPdfUrl.trim();
      this.currentTitle = `测试 PDF - ${this.currentPdfUrl}`;
      this.pdfKey++; // 强制重新渲染组件
      
      this.addLog('info', `开始加载 PDF: ${this.currentPdfUrl}`);
    },

    loadPresetPdf(url) {
      this.testPdfUrl = url;
      this.currentPdfUrl = url;
      this.currentTitle = `预设 PDF - ${url}`;
      this.pdfKey++;
      
      this.addLog('info', `加载预设 PDF: ${url}`);
    },

    clearLogs() {
      this.debugLogs = [];
      this.addLog('info', '日志已清除');
    }
  }
}
</script>

<style scoped>
.pdf-test-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.test-header {
  text-align: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #e0e0e0;
}

.test-header h1 {
  color: #333;
  margin-bottom: 10px;
}

.test-header p {
  color: #666;
  font-size: 16px;
}

.test-controls {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.url-input {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
}

.url-input label {
  font-weight: bold;
  min-width: 80px;
}

.url-field {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.load-btn, .preset-btn {
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.2s;
}

.load-btn:hover, .preset-btn:hover {
  background: #0056b3;
}

.preset-pdfs h3 {
  margin-bottom: 10px;
  color: #333;
}

.preset-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  margin-bottom: 20px;
}

.preset-btn {
  background: #28a745;
}

.preset-btn:hover {
  background: #1e7e34;
}

.test-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 10px;
}

.info-item {
  padding: 10px;
  background: white;
  border-radius: 4px;
  border-left: 4px solid #007bff;
}

.pdf-viewer-container {
  height: 600px;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 20px;
}

.no-pdf {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #666;
}

.no-pdf i {
  font-size: 64px;
  margin-bottom: 20px;
  color: #ccc;
}

.debug-panel {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.debug-panel h3 {
  margin-bottom: 15px;
  color: #333;
}

.debug-content {
  max-height: 300px;
  overflow-y: auto;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 10px;
}

.debug-log {
  display: flex;
  gap: 10px;
  padding: 5px 0;
  border-bottom: 1px solid #eee;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.debug-log:last-child {
  border-bottom: none;
}

.log-time {
  color: #666;
  min-width: 80px;
}

.log-message {
  flex: 1;
}

.debug-log.info .log-message {
  color: #007bff;
}

.debug-log.warn .log-message {
  color: #ffc107;
}

.debug-log.error .log-message {
  color: #dc3545;
}

.clear-logs-btn {
  padding: 6px 12px;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.clear-logs-btn:hover {
  background: #545b62;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .pdf-test-page {
    padding: 10px;
  }
  
  .url-input {
    flex-direction: column;
    align-items: stretch;
  }
  
  .preset-buttons {
    flex-direction: column;
  }
  
  .test-info {
    grid-template-columns: 1fr;
  }
  
  .pdf-viewer-container {
    height: 400px;
  }
}
</style>
