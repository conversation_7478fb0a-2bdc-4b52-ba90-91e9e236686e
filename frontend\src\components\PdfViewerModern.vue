<template>
  <div class="pdf-viewer-modern">
    <div class="pdf-toolbar">
      <div class="pdf-info">
        <span v-if="pageNum && pageCount">{{ pageNum }} / {{ pageCount }}</span>
        <span v-else-if="loading">加载中...</span>
        <span v-else>未加载</span>
      </div>
      <div class="pdf-controls">
        <button @click="prevPage" :disabled="pageNum <= 1 || loading" class="pdf-btn">
          <i class="fas fa-chevron-left"></i> 上一页
        </button>
        <button @click="nextPage" :disabled="pageNum >= pageCount || loading" class="pdf-btn">
          下一页 <i class="fas fa-chevron-right"></i>
        </button>
        <button @click="zoomOut" :disabled="loading" class="pdf-btn">
          <i class="fas fa-search-minus"></i>
        </button>
        <span class="zoom-info">{{ Math.round(scale * 100) }}%</span>
        <button @click="zoomIn" :disabled="loading" class="pdf-btn">
          <i class="fas fa-search-plus"></i>
        </button>
        <button @click="downloadPdf" :disabled="loading" class="pdf-btn">
          <i class="fas fa-download"></i> 下载
        </button>
      </div>
    </div>
    
    <div class="pdf-container" ref="pdfContainer">
      <div v-if="loading" class="pdf-loading">
        <div class="spinner"></div>
        <p>正在加载 PDF...</p>
      </div>
      
      <div v-else-if="error" class="pdf-error">
        <i class="fas fa-exclamation-triangle"></i>
        <h4>加载失败</h4>
        <p>{{ error }}</p>
        <button @click="retry" class="retry-btn">重试</button>
        <button @click="downloadPdf" class="download-btn">直接下载</button>
      </div>
      
      <div v-else class="pdf-content">
        <canvas 
          ref="pdfCanvas" 
          class="pdf-canvas"
          :style="canvasStyle"
        ></canvas>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PdfViewerModern',
  props: {
    pdfUrl: {
      type: String,
      required: true
    },
    title: {
      type: String,
      default: 'PDF 文档'
    }
  },
  data() {
    return {
      loading: false,
      error: null,
      pageNum: 0,
      pageCount: 0,
      scale: 1.0
    };
  },
  // 使用非响应式属性存储 PDF 对象，避免 Vue 响应式系统破坏私有成员
  created() {
    this.pdfDocument = null;
    this.currentPage = null;
    this.renderTask = null;
    this.pdfjsLib = null;
  },
  computed: {
    canvasStyle() {
      return {
        maxWidth: '100%',
        height: 'auto',
        boxShadow: '0 4px 8px rgba(0,0,0,0.1)'
      };
    }
  },
  async mounted() {
    await this.initializePdfJs();
    if (this.pdfUrl && !this.error) {
      await this.loadDocument();
    }
  },
  beforeUnmount() {
    this.cleanup();
  },
  watch: {
    pdfUrl: {
      handler(newUrl) {
        if (newUrl) {
          this.loadDocument();
        }
      }
    }
  },
  methods: {
    // 初始化 PDF.js 库
    async initializePdfJs() {
      try {
        console.log('正在初始化 PDF.js...');
        
        // 动态导入 PDF.js
        this.pdfjsLib = await import('pdfjs-dist');
        
        // 设置 worker 路径
        this.pdfjsLib.GlobalWorkerOptions.workerSrc = new URL(
          'pdfjs-dist/build/pdf.worker.min.mjs',
          import.meta.url
        ).href;
        
        console.log('PDF.js 初始化成功');
        console.log('版本:', this.pdfjsLib.version);
        console.log('Worker:', this.pdfjsLib.GlobalWorkerOptions.workerSrc);
        
      } catch (err) {
        console.error('PDF.js 初始化失败:', err);
        this.error = `PDF.js 初始化失败: ${err.message}`;
      }
    },

    // 加载 PDF 文档
    async loadDocument() {
      if (!this.pdfjsLib) {
        this.error = 'PDF.js 未初始化';
        return;
      }

      this.cleanup();
      this.loading = true;
      this.error = null;
      this.pageNum = 0;
      this.pageCount = 0;

      try {
        console.log('开始加载文档:', this.pdfUrl);

        // 创建加载任务
        const loadingTask = this.pdfjsLib.getDocument({
          url: this.pdfUrl,
          httpHeaders: {},
          withCredentials: false
        });

        // 等待文档加载完成
        this.pdfDocument = await loadingTask.promise;
        this.pageCount = this.pdfDocument.numPages;
        
        console.log('文档加载成功，总页数:', this.pageCount);

        // 加载并渲染第一页
        await this.loadPage(1);
        
        this.loading = false;

      } catch (err) {
        console.error('文档加载失败:', err);
        this.error = `加载失败: ${err.message}`;
        this.loading = false;
      }
    },

    // 加载指定页面
    async loadPage(pageNumber) {
      if (!this.pdfDocument || pageNumber < 1 || pageNumber > this.pageCount) {
        return;
      }

      try {
        console.log('加载页面:', pageNumber);

        // 取消当前渲染任务
        if (this.renderTask) {
          this.renderTask.cancel();
          this.renderTask = null;
        }

        // 获取页面
        this.currentPage = await this.pdfDocument.getPage(pageNumber);
        console.log('页面加载成功');

        // 渲染页面
        await this.renderPage();
        
        this.pageNum = pageNumber;

      } catch (err) {
        console.error('页面加载失败:', err);
        this.error = `页面 ${pageNumber} 加载失败: ${err.message}`;
      }
    },

    // 渲染当前页面到 Canvas
    async renderPage() {
      if (!this.currentPage) {
        return;
      }

      try {
        console.log('开始渲染页面，缩放比例:', this.scale);

        const canvas = this.$refs.pdfCanvas;
        if (!canvas) {
          throw new Error('Canvas 元素未找到');
        }

        const context = canvas.getContext('2d');

        // 获取视口
        const viewport = this.currentPage.getViewport({ scale: this.scale });
        
        // 支持高 DPI 屏幕
        const outputScale = window.devicePixelRatio || 1;

        // 设置 canvas 尺寸
        canvas.width = Math.floor(viewport.width * outputScale);
        canvas.height = Math.floor(viewport.height * outputScale);
        canvas.style.width = Math.floor(viewport.width) + "px";
        canvas.style.height = Math.floor(viewport.height) + "px";

        // 设置变换矩阵
        const transform = outputScale !== 1 ? [outputScale, 0, 0, outputScale, 0, 0] : null;

        // 渲染配置
        const renderContext = {
          canvasContext: context,
          transform: transform,
          viewport: viewport
        };

        console.log('渲染配置:', renderContext);

        // 开始渲染
        this.renderTask = this.currentPage.render(renderContext);
        await this.renderTask.promise;
        this.renderTask = null;

        console.log('页面渲染完成');

      } catch (err) {
        if (err.name !== 'RenderingCancelledException') {
          console.error('渲染失败:', err);
          this.error = `渲染失败: ${err.message}`;
        }
      }
    },

    // 上一页
    async prevPage() {
      if (this.pageNum > 1) {
        await this.loadPage(this.pageNum - 1);
      }
    },

    // 下一页
    async nextPage() {
      if (this.pageNum < this.pageCount) {
        await this.loadPage(this.pageNum + 1);
      }
    },

    // 放大
    async zoomIn() {
      this.scale = Math.min(this.scale * 1.2, 3.0);
      await this.renderPage();
    },

    // 缩小
    async zoomOut() {
      this.scale = Math.max(this.scale / 1.2, 0.3);
      await this.renderPage();
    },

    // 重试
    async retry() {
      await this.loadDocument();
    },

    // 下载 PDF
    downloadPdf() {
      if (this.pdfUrl) {
        const link = document.createElement('a');
        link.href = this.pdfUrl;
        link.download = this.title + '.pdf';
        link.click();
      }
    },

    // 清理资源
    cleanup() {
      if (this.renderTask) {
        this.renderTask.cancel();
        this.renderTask = null;
      }

      if (this.currentPage) {
        this.currentPage = null;
      }

      if (this.pdfDocument) {
        this.pdfDocument.destroy();
        this.pdfDocument = null;
      }
    }
  }
}
</script>

<style scoped>
.pdf-viewer-modern {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.pdf-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #e1e5e9;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.pdf-info {
  font-weight: 500;
  color: #374151;
}

.pdf-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pdf-btn {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  color: #374151;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.pdf-btn:hover:not(:disabled) {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.pdf-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.zoom-info {
  padding: 6px 8px;
  font-size: 14px;
  color: #6b7280;
  min-width: 50px;
  text-align: center;
}

.pdf-container {
  flex: 1;
  overflow: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.pdf-content {
  display: flex;
  justify-content: center;
}

.pdf-canvas {
  border-radius: 4px;
}

.pdf-loading {
  text-align: center;
  color: #6b7280;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.pdf-error {
  text-align: center;
  color: #dc2626;
}

.pdf-error i {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}

.pdf-error h4 {
  margin: 0 0 8px 0;
  color: #374151;
}

.retry-btn, .download-btn {
  margin: 8px 4px;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
}

.retry-btn {
  background: #3b82f6;
  color: white;
}

.download-btn {
  background: #10b981;
  color: white;
}

.retry-btn:hover {
  background: #2563eb;
}

.download-btn:hover {
  background: #059669;
}
</style>
