declare module 'pdfjs-dist' {
  export interface PDFDocumentProxy {
    numPages: number;
    getPage(pageNumber: number): Promise<PDFPageProxy>;
    destroy(): void;
  }

  export interface PDFPageProxy {
    getViewport(params: { scale: number }): PDFPageViewport;
    render(renderContext: RenderContext): RenderTask;
  }

  export interface PDFPageViewport {
    width: number;
    height: number;
  }

  export interface RenderContext {
    canvasContext: CanvasRenderingContext2D;
    viewport: PDFPageViewport;
    transform?: number[];
  }

  export interface RenderTask {
    promise: Promise<void>;
    cancel(): void;
  }

  export interface PDFDocumentLoadingTask {
    promise: Promise<PDFDocumentProxy>;
  }

  export interface LoadingTaskOptions {
    url: string;
    httpHeaders?: Record<string, string>;
    withCredentials?: boolean;
  }

  export function getDocument(options: LoadingTaskOptions | string): PDFDocumentLoadingTask;

  export const GlobalWorkerOptions: {
    workerSrc: string;
  };

  export const version: string;
}
