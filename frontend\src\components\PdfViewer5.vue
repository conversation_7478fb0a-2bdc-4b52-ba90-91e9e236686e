<template>
  <div class="pdf-viewer-5">
    <div class="pdf-toolbar">
      <div class="pdf-info">
        <span v-if="pageNum">{{ pageNum }} / {{ pageCount }}</span>
        <span v-else>加载中...</span>
      </div>
      <div class="pdf-controls">
        <button @click="prevPage" :disabled="pageNum <= 1 || loading" class="pdf-btn">
          <i class="fas fa-chevron-left"></i>
        </button>
        <button @click="nextPage" :disabled="pageNum >= pageCount || loading" class="pdf-btn">
          <i class="fas fa-chevron-right"></i>
        </button>
        <button @click="zoomIn" :disabled="loading" class="pdf-btn">
          <i class="fas fa-search-plus"></i>
        </button>
        <button @click="zoomOut" :disabled="loading" class="pdf-btn">
          <i class="fas fa-search-minus"></i>
        </button>
        <button @click="downloadPdf" :disabled="loading" class="pdf-btn">
          <i class="fas fa-download"></i>
        </button>
      </div>
    </div>
    
    <div class="pdf-container" ref="pdfContainer">
      <div v-if="loading" class="pdf-loading">
        <i class="fas fa-spinner fa-spin"></i>
        <span>PDF 加载中...</span>
      </div>
      <div v-if="error" class="pdf-error">
        <i class="fas fa-exclamation-triangle"></i>
        <h4>加载失败</h4>
        <p>{{ error }}</p>
        <button @click="downloadPdf" class="pdf-download-btn">
          <i class="fas fa-download"></i> 下载PDF
        </button>
      </div>
      <canvas ref="pdfCanvas" class="pdf-canvas" v-show="!loading && !error"></canvas>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PdfViewer5',
  props: {
    pdfUrl: {
      type: String,
      required: true
    },
    title: {
      type: String,
      default: '文档预览'
    }
  },
  data() {
    return {
      loading: true,
      error: null,
      pageNum: 1,
      pageCount: 0,
      scale: 1.0,
      pdfDoc: null,
      currentRenderTask: null,
      pdfjsLib: null
    };
  },
  async mounted() {
    await this.initPdfJs();
    if (!this.error) {
      await this.loadPdf();
    }
  },
  beforeUnmount() {
    this.cleanup();
  },
  watch: {
    pdfUrl: {
      handler(newUrl, oldUrl) {
        if (newUrl !== oldUrl) {
          this.loadPdf();
        }
      }
    }
  },
  methods: {
    // 初始化 PDF.js
    async initPdfJs() {
      try {
        console.log('初始化 PDF.js...');

        // 动态导入 PDF.js
        this.pdfjsLib = await import('pdfjs-dist');

        // 设置 worker
        this.pdfjsLib.GlobalWorkerOptions.workerSrc = new URL(
          'pdfjs-dist/build/pdf.worker.min.mjs',
          import.meta.url
        ).href;

        console.log('PDF.js 初始化成功，版本:', this.pdfjsLib.version);
        console.log('Worker URL:', this.pdfjsLib.GlobalWorkerOptions.workerSrc);

      } catch (err) {
        console.error('PDF.js 初始化失败:', err);
        this.error = 'PDF.js 库加载失败';
        this.loading = false;
      }
    },

    // 加载PDF文档
    async loadPdf() {
      if (!this.pdfjsLib) {
        this.error = 'PDF.js 未初始化';
        this.loading = false;
        return;
      }

      this.cleanup();
      this.loading = true;
      this.error = null;

      try {
        console.log('开始加载 PDF:', this.pdfUrl);

        // 使用 PDF.js 5.x 加载文档
        const loadingTask = this.pdfjsLib.getDocument({
          url: this.pdfUrl,
          httpHeaders: {},
          withCredentials: false
        });

        this.pdfDoc = await loadingTask.promise;
        this.pageCount = this.pdfDoc.numPages;

        console.log('PDF 加载成功，页数:', this.pageCount);

        // 渲染第一页
        this.pageNum = 1;
        await this.renderPage(1);

        this.loading = false;
      } catch (err) {
        console.error('PDF加载失败:', err);
        this.error = err.message || '无法加载PDF文档';
        this.loading = false;
      }
    },

    // 渲染指定页面
    async renderPage(num) {
      if (!this.pdfDoc) {
        console.warn('PDF 文档未加载');
        return;
      }

      try {
        console.log('开始渲染页面:', num);

        // 取消当前渲染任务
        if (this.currentRenderTask) {
          try {
            this.currentRenderTask.cancel();
          } catch (e) {
            console.warn('取消渲染任务失败:', e);
          }
          this.currentRenderTask = null;
        }

        // 获取页面 - 使用 await 确保页面完全加载
        const page = await this.pdfDoc.getPage(num);
        console.log('页面对象获取成功:', page);

        // 获取视口
        const viewport = page.getViewport({ scale: this.scale });
        console.log('视口信息:', viewport);

        // 设置canvas
        const canvas = this.$refs.pdfCanvas;
        if (!canvas) {
          console.error('Canvas 元素未找到');
          return;
        }

        const context = canvas.getContext('2d');

        // 设置canvas尺寸
        canvas.height = viewport.height;
        canvas.width = viewport.width;

        console.log('Canvas 尺寸设置:', canvas.width, 'x', canvas.height);

        // 清除画布
        context.clearRect(0, 0, canvas.width, canvas.height);

        // 渲染配置
        const renderContext = {
          canvasContext: context,
          viewport: viewport
        };

        console.log('开始渲染...');

        // 开始渲染
        const renderTask = page.render(renderContext);
        this.currentRenderTask = renderTask;

        await renderTask.promise;
        this.currentRenderTask = null;

        this.pageNum = num;
        console.log('页面渲染成功:', num);
      } catch (err) {
        if (err && err.name !== 'RenderingCancelledException') {
          console.error('页面渲染失败:', err);
          console.error('错误详情:', err.stack);
          this.error = `渲染页面 ${num} 失败: ${err.message}`;
        }
      }
    },

    // 上一页
    prevPage() {
      if (this.pageNum <= 1) return;
      this.renderPage(this.pageNum - 1);
    },

    // 下一页
    nextPage() {
      if (this.pageNum >= this.pageCount) return;
      this.renderPage(this.pageNum + 1);
    },

    // 放大
    zoomIn() {
      this.scale = Math.min(this.scale + 0.2, 3.0);
      this.renderPage(this.pageNum);
    },

    // 缩小
    zoomOut() {
      this.scale = Math.max(this.scale - 0.2, 0.5);
      this.renderPage(this.pageNum);
    },

    // 下载PDF
    downloadPdf() {
      if (this.pdfUrl) {
        window.open(this.pdfUrl, '_blank');
      }
    },

    // 清理资源
    cleanup() {
      if (this.currentRenderTask) {
        try {
          this.currentRenderTask.cancel();
        } catch (e) {
          console.warn('清理渲染任务失败:', e);
        }
        this.currentRenderTask = null;
      }

      if (this.pdfDoc) {
        try {
          this.pdfDoc.destroy();
        } catch (e) {
          console.warn('清理 PDF 文档失败:', e);
        }
        this.pdfDoc = null;
      }
    }
  }
}
</script>

<style scoped>
.pdf-viewer-5 {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  background: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;
}

.pdf-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: #fff;
  border-bottom: 1px solid #e0e0e0;
  flex-shrink: 0;
}

.pdf-info {
  font-size: 14px;
  color: #555;
}

.pdf-controls {
  display: flex;
  gap: 8px;
}

.pdf-btn {
  background: none;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 5px 10px;
  cursor: pointer;
  color: #4682B4;
  transition: all 0.2s;
  font-size: 12px;
}

.pdf-btn:hover:not(:disabled) {
  background: #f0f0f0;
  color: #1E90FF;
}

.pdf-btn:disabled {
  color: #ccc;
  cursor: not-allowed;
}

.pdf-container {
  flex: 1;
  overflow: auto;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 20px;
  position: relative;
}

.pdf-canvas {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  background: white;
  max-width: 100%;
  height: auto;
}

.pdf-loading, .pdf-error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #666;
}

.pdf-loading i, .pdf-error i {
  font-size: 32px;
  margin-bottom: 15px;
  display: block;
}

.pdf-error i {
  color: #dc3545;
}

.pdf-error h4 {
  margin: 10px 0;
  color: #333;
}

.pdf-download-btn {
  background: #4682B4;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  margin-top: 15px;
  cursor: pointer;
  transition: background 0.2s;
}

.pdf-download-btn:hover {
  background: #1E90FF;
}
</style>
