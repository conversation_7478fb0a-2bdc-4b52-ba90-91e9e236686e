<template>
  <div class="pdf-viewer-compat">
    <div class="pdf-toolbar">
      <div class="pdf-info">
        <span v-if="pageNum">{{ pageNum }} / {{ pageCount }}</span>
        <span v-else>加载中...</span>
      </div>
      <div class="pdf-controls">
        <button @click="prevPage" :disabled="pageNum <= 1 || loading" class="pdf-btn">
          <i class="fas fa-chevron-left"></i>
        </button>
        <button @click="nextPage" :disabled="pageNum >= pageCount || loading" class="pdf-btn">
          <i class="fas fa-chevron-right"></i>
        </button>
        <button @click="zoomIn" :disabled="loading" class="pdf-btn">
          <i class="fas fa-search-plus"></i>
        </button>
        <button @click="zoomOut" :disabled="loading" class="pdf-btn">
          <i class="fas fa-search-minus"></i>
        </button>
        <button @click="downloadPdf" :disabled="loading" class="pdf-btn">
          <i class="fas fa-download"></i>
        </button>
      </div>
    </div>
    
    <div class="pdf-container" ref="pdfContainer">
      <div v-if="loading" class="pdf-loading">
        <i class="fas fa-spinner fa-spin"></i>
        <span>PDF 加载中...</span>
      </div>
      <div v-if="error" class="pdf-error">
        <i class="fas fa-exclamation-triangle"></i>
        <h4>加载失败</h4>
        <p>{{ error }}</p>
        <button @click="downloadPdf" class="pdf-download-btn">
          <i class="fas fa-download"></i> 下载PDF
        </button>
      </div>
      <canvas ref="pdfCanvas" class="pdf-canvas" v-show="!loading && !error"></canvas>
    </div>
  </div>
</template>

<script>
// 兼容性版本 - 专门针对钉钉等环境优化
export default {
  name: 'PdfViewerCompat',
  props: {
    pdfUrl: {
      type: String,
      required: true
    },
    title: {
      type: String,
      default: '文档预览'
    }
  },
  data() {
    return {
      loading: true,
      error: null,
      pageNum: 1,
      pageCount: 0,
      scale: 1.0,
      pdfDoc: null,
      pdfTask: null,
      currentRenderTask: null,
      pdfjsLib: null
    };
  },
  async mounted() {
    await this.initPdfJs();
    await this.loadPdf();
  },
  beforeUnmount() {
    this.cleanup();
  },
  watch: {
    pdfUrl: {
      handler(newUrl, oldUrl) {
        if (newUrl !== oldUrl) {
          this.loadPdf();
        }
      }
    }
  },
  methods: {
    // 动态加载 PDF.js 以提高兼容性
    async initPdfJs() {
      try {
        // 动态导入 PDF.js
        this.pdfjsLib = await import('pdfjs-dist');

        // 使用本地worker文件，完全避免CDN依赖
        await this.setupWorker();

      } catch (err) {
        console.error('PDF.js 初始化失败:', err);
        this.error = 'PDF.js 库加载失败';
        this.loading = false;
      }
    },

    // 设置 PDF.js Worker，优先使用本地文件
    async setupWorker() {
      try {
        // 方案1: 使用 Vite 的静态资源导入
        const workerUrl = new URL('pdfjs-dist/build/pdf.worker.min.js', import.meta.url);
        this.pdfjsLib.GlobalWorkerOptions.workerSrc = workerUrl.href;
        console.log('使用本地 PDF.js worker:', workerUrl.href);
      } catch (err1) {
        console.warn('方案1失败，尝试方案2:', err1);
        try {
          // 方案2: 直接使用相对路径
          this.pdfjsLib.GlobalWorkerOptions.workerSrc = '/node_modules/pdfjs-dist/build/pdf.worker.min.js';
          console.log('使用相对路径 PDF.js worker');
        } catch (err2) {
          console.warn('方案2失败，尝试方案3:', err2);
          try {
            // 方案3: 使用动态导入的 worker
            const workerModule = await import('pdfjs-dist/build/pdf.worker.min.js?url');
            this.pdfjsLib.GlobalWorkerOptions.workerSrc = workerModule.default;
            console.log('使用动态导入 PDF.js worker');
          } catch (err3) {
            console.warn('方案3失败，使用内联 worker:', err3);
            // 方案4: 使用内联 worker（最后的备选方案）
            this.pdfjsLib.GlobalWorkerOptions.workerSrc = this.createInlineWorker();
            console.log('使用内联 PDF.js worker');
          }
        }
      }
    },

    // 创建内联 worker 作为最后的备选方案
    createInlineWorker() {
      try {
        // 创建一个简单的 worker blob
        const workerBlob = new Blob([`
          importScripts('${new URL('pdfjs-dist/build/pdf.worker.min.js', import.meta.url).href}');
        `], { type: 'application/javascript' });
        return URL.createObjectURL(workerBlob);
      } catch (err) {
        console.error('创建内联 worker 失败:', err);
        // 如果所有方案都失败，返回一个空的 worker URL
        // PDF.js 会尝试使用主线程渲染（性能较差但可用）
        return '';
      }
    },

    // 加载PDF文档
    async loadPdf() {
      if (!this.pdfjsLib) {
        this.error = 'PDF.js 未初始化';
        this.loading = false;
        return;
      }

      this.cleanup();
      this.loading = true;
      this.error = null;
      
      try {
        // 使用兼容性配置加载PDF
        const loadingTask = this.pdfjsLib.getDocument({
          url: this.pdfUrl,
          // 禁用一些可能导致兼容性问题的功能
          disableFontFace: true,
          disableRange: false,
          disableStream: false,
          // 设置较小的最大图像大小
          maxImageSize: 1024 * 1024,
          // 禁用自动获取
          disableAutoFetch: false,
          // 设置超时
          httpHeaders: {},
          withCredentials: false
        });
        
        this.pdfTask = loadingTask;
        this.pdfDoc = await loadingTask.promise;
        this.pageCount = this.pdfDoc.numPages;
        
        // 渲染第一页
        this.pageNum = 1;
        await this.renderPage(1);
        
        this.loading = false;
      } catch (err) {
        console.error('PDF加载失败:', err);
        this.error = err.message || '无法加载PDF文档';
        this.loading = false;
      }
    },

    // 渲染指定页面
    async renderPage(num) {
      if (!this.pdfDoc) return;
      
      try {
        // 取消当前渲染任务
        if (this.currentRenderTask) {
          try {
            this.currentRenderTask.cancel();
          } catch (e) {
            // 忽略取消错误
          }
          this.currentRenderTask = null;
        }
        
        // 获取页面
        const page = await this.pdfDoc.getPage(num);
        
        // 获取视口 - 兼容新版本的 API
        const viewport = page.getViewport({ scale: this.scale });
        
        // 设置canvas
        const canvas = this.$refs.pdfCanvas;
        if (!canvas) return;
        
        const context = canvas.getContext('2d');
        
        // 设置canvas尺寸
        canvas.height = viewport.height;
        canvas.width = viewport.width;
        
        // 清除画布
        context.clearRect(0, 0, canvas.width, canvas.height);
        
        // 渲染配置
        const renderContext = {
          canvasContext: context,
          viewport: viewport
        };
        
        // 开始渲染
        this.currentRenderTask = page.render(renderContext);
        await this.currentRenderTask.promise;
        this.currentRenderTask = null;

        this.pageNum = num;
      } catch (err) {
        if (err && err.name !== 'RenderingCancelledException') {
          console.error('页面渲染失败:', err);
          this.error = `渲染页面 ${num} 失败`;
        }
      }
    },

    // 上一页
    prevPage() {
      if (this.pageNum <= 1) return;
      this.renderPage(this.pageNum - 1);
    },

    // 下一页
    nextPage() {
      if (this.pageNum >= this.pageCount) return;
      this.renderPage(this.pageNum + 1);
    },

    // 放大
    zoomIn() {
      this.scale = Math.min(this.scale + 0.2, 3.0);
      this.renderPage(this.pageNum);
    },

    // 缩小
    zoomOut() {
      this.scale = Math.max(this.scale - 0.2, 0.5);
      this.renderPage(this.pageNum);
    },

    // 下载PDF
    downloadPdf() {
      if (this.pdfUrl) {
        window.open(this.pdfUrl, '_blank');
      }
    },

    // 清理资源
    cleanup() {
      // 清理渲染任务
      if (this.currentRenderTask) {
        try {
          this.currentRenderTask.cancel();
        } catch (e) {
          // 忽略错误
        }
        this.currentRenderTask = null;
      }
      
      // 清理PDF文档
      if (this.pdfDoc) {
        try {
          this.pdfDoc.destroy();
        } catch (e) {
          // 忽略错误
        }
        this.pdfDoc = null;
      }
      
      // 清理加载任务
      if (this.pdfTask) {
        try {
          this.pdfTask.destroy();
        } catch (e) {
          // 忽略错误
        }
        this.pdfTask = null;
      }
    }
  }
}
</script>

<style scoped>
.pdf-viewer-compat {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  background: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;
}

.pdf-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: #fff;
  border-bottom: 1px solid #e0e0e0;
  flex-shrink: 0;
}

.pdf-info {
  font-size: 14px;
  color: #555;
}

.pdf-controls {
  display: flex;
  gap: 8px;
}

.pdf-btn {
  background: none;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 5px 10px;
  cursor: pointer;
  color: #4682B4;
  transition: all 0.2s;
  font-size: 12px;
}

.pdf-btn:hover:not(:disabled) {
  background: #f0f0f0;
  color: #1E90FF;
}

.pdf-btn:disabled {
  color: #ccc;
  cursor: not-allowed;
}

.pdf-container {
  flex: 1;
  overflow: auto;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 20px;
  position: relative;
}

.pdf-canvas {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  background: white;
  max-width: 100%;
  height: auto;
}

.pdf-loading, .pdf-error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #666;
}

.pdf-loading i, .pdf-error i {
  font-size: 32px;
  margin-bottom: 15px;
  display: block;
}

.pdf-error i {
  color: #dc3545;
}

.pdf-error h4 {
  margin: 10px 0;
  color: #333;
}

.pdf-download-btn {
  background: #4682B4;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  margin-top: 15px;
  cursor: pointer;
  transition: background 0.2s;
}

.pdf-download-btn:hover {
  background: #1E90FF;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .pdf-toolbar {
    padding: 8px 10px;
  }
  
  .pdf-controls {
    gap: 4px;
  }
  
  .pdf-btn {
    padding: 4px 8px;
    font-size: 11px;
  }
  
  .pdf-container {
    padding: 10px;
  }
}
</style>
